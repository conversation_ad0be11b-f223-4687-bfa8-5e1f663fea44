#!/usr/bin/env python3

from pwn import *
from hashlib import sha512

def main():
    # Connect to the server
    host = "stormtown-of-constant-abundance.gpn23.ctf.kitctf.de"
    port = 443
    
    print(f"Connecting to {host}:{port}")
    
    try:
        # Use SSL connection
        r = remote(host, port, ssl=True)
        
        # Read the initial output
        r.recvuntil(b"Welcome to the Pad Server!\n")
        
        # Get the XORed flag
        xored_flag_hex = r.recvline().strip().decode()
        print(f"XORed flag: {xored_flag_hex}")
        
        # Get the encrypted challenge
        encrypted_challenge_hex = r.recvline().strip().decode()
        print(f"Encrypted challenge: {encrypted_challenge_hex}")
        
        encrypted_challenge = bytes.fromhex(encrypted_challenge_hex)
        print(f"Encrypted challenge length: {len(encrypted_challenge)} bytes")
        print(f"Number of blocks: {len(encrypted_challenge) // 16}")
        
        # Test various modifications to understand oracle behavior
        tests = [
            ("Original", encrypted_challenge),
            ("All zeros", b'\x00' * 32),  # Two blocks of zeros
            ("Random bytes", b'\x41' * 32),  # Two blocks of 'A'
            ("Last block modified", encrypted_challenge[:-16] + b'\x00' * 16),
            ("Second to last modified", encrypted_challenge[:-32] + b'\x00' * 16 + encrypted_challenge[-16:]),
        ]
        
        for test_name, test_data in tests:
            print(f"\nTesting: {test_name}")
            print(f"Length: {len(test_data)} bytes")
            
            if len(test_data) % 16 != 0:
                print("Skipping - not multiple of 16 bytes")
                continue
                
            r.sendlineafter(b"speak to the oracle: ", test_data.hex().encode())
            response = r.recvline()
            print(f"Oracle response: {response.strip()}")
        
        # Try to find a case that returns False
        print("\nTrying to find False response...")
        
        # Test with just one block (should fail)
        single_block = encrypted_challenge[:16]
        print(f"Testing single block: {len(single_block)} bytes")
        r.sendlineafter(b"speak to the oracle: ", single_block.hex().encode())
        response = r.recvline()
        print(f"Oracle response: {response.strip()}")
        
        # Test with invalid length
        print("Testing invalid length (15 bytes)...")
        try:
            r.sendlineafter(b"speak to the oracle: ", (b'\x00' * 15).hex().encode())
            response = r.recvline()
            print(f"Oracle response: {response.strip()}")
        except:
            print("Failed to send invalid length")
        
        r.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
