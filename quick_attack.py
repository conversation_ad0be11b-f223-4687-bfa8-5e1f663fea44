#!/usr/bin/env python3

from pwn import *
from hashlib import sha512
import string

def decrypt_last_byte(oracle_func, ciphertext):
    """Quickly decrypt just the last byte to understand padding"""
    blocks = [ciphertext[i:i+16] for i in range(0, len(ciphertext), 16)]
    
    if len(blocks) < 2:
        return None
    
    # Get last block and second-to-last block
    target_block = blocks[-1]
    prev_block = blocks[-2]
    
    print(f"Trying to find padding length...")
    
    # Try to find the padding by testing different values for the last byte
    modified_prev = bytearray(prev_block)
    
    for guess in range(1, 17):  # Padding can be 1-16
        # Set the last byte to create padding of length 'guess'
        modified_prev[-1] = prev_block[-1] ^ guess ^ guess
        
        test_ciphertext = bytes(modified_prev) + target_block
        
        if oracle_func(test_ciphertext):
            print(f"Found padding length: {guess}")
            return guess
    
    return None

def try_common_endings(oracle_func, ciphertext):
    """Try common German word endings to speed up the attack"""
    blocks = [ciphertext[i:i+16] for i in range(0, len(ciphertext), 16)]
    
    if len(blocks) < 2:
        return None
    
    # Common German word endings
    common_endings = [
        "ung", "tion", "heit", "keit", "lich", "isch", "ende", "ande", 
        "erde", "inde", "unde", "ente", "ante", "inte", "onte", "unte",
        "der", "die", "das", "den", "dem", "des", "ein", "eine", "einen",
        "und", "mit", "von", "auf", "für", "bei", "nach", "über", "unter"
    ]
    
    # Get last few blocks
    target_block = blocks[-1]
    prev_block = blocks[-2]
    
    print("Trying common German endings...")
    
    for ending in common_endings:
        # Try different padding lengths
        for pad_len in range(1, 17):
            if len(ending) + pad_len > 16:
                continue
                
            # Create the expected plaintext block
            plaintext = ending.encode() + bytes([pad_len] * pad_len)
            plaintext = plaintext.rjust(16, b'A')  # Pad with 'A' if needed
            
            # Calculate what the previous block should be
            # plaintext = intermediate XOR prev_block
            # So: prev_block = intermediate XOR plaintext
            
            # We don't know the intermediate, but we can try to construct it
            # by assuming the oracle will tell us if we're right
            
            # This is getting complex, let me try a simpler approach
            pass
    
    return None

def main():
    # Connect to the server
    host = "stormtown-of-constant-abundance.gpn23.ctf.kitctf.de"
    port = 443
    
    print(f"Connecting to {host}:{port}")
    
    try:
        # Use SSL connection
        r = remote(host, port, ssl=True)
        
        # Read the initial output
        r.recvuntil(b"Welcome to the Pad Server!\n")
        
        # Get the XORed flag
        xored_flag_hex = r.recvline().strip().decode()
        print(f"XORed flag: {xored_flag_hex}")
        
        # Get the encrypted challenge
        encrypted_challenge_hex = r.recvline().strip().decode()
        print(f"Encrypted challenge: {encrypted_challenge_hex}")
        
        encrypted_challenge = bytes.fromhex(encrypted_challenge_hex)
        print(f"Encrypted challenge length: {len(encrypted_challenge)} bytes")
        print(f"Number of blocks: {len(encrypted_challenge) // 16}")
        
        # Define oracle function
        def oracle(ciphertext):
            try:
                r.sendlineafter(b"speak to the oracle: ", ciphertext.hex().encode())
                response = r.recvline()
                return b"True" in response
            except Exception as e:
                print(f"Oracle error: {e}")
                return False
        
        # Try to understand the padding
        padding_len = decrypt_last_byte(oracle, encrypted_challenge)
        
        if padding_len:
            print(f"Detected padding length: {padding_len}")
            
            # Now we know the last 'padding_len' bytes are padding
            # This gives us some information about the plaintext length
            total_plaintext_len = len(encrypted_challenge) - 16 - padding_len  # -16 for IV
            print(f"Estimated plaintext length: {total_plaintext_len}")
        
        # Try some educated guesses based on the challenge description
        # The challenge mentions German words, so let's try some common patterns
        
        # Since we know the structure: xor(sha512(text[:-3].encode("utf-8")).digest(), FLAG.encode())
        # And we have the XORed result, we can try to guess the text
        
        # Let's try some common German text patterns
        common_texts = [
            "DieKatzeistaufdemDach",
            "EinManneinWorteinTat", 
            "DeutschlandüberAlles",
            "WirsinddieChampions",
            "DasisteinTest",
            "HalloWelt",
            "GutenTag",
            "AufWiedersehen"
        ]
        
        print("\nTrying common German text patterns...")
        
        for text in common_texts:
            # Try different lengths by adding characters
            for extra_chars in range(0, 10):
                test_text = text + "A" * extra_chars
                
                # Try the hash calculation
                text_for_hash = test_text[:-3] if len(test_text) > 3 else test_text
                hash_digest = sha512(text_for_hash.encode("utf-8")).digest()
                
                xored_flag_bytes = bytes.fromhex(xored_flag_hex)
                
                # XOR to get potential flag
                if len(hash_digest) == len(xored_flag_bytes):
                    flag_bytes = bytes(a ^ b for a, b in zip(hash_digest, xored_flag_bytes))
                    
                    try:
                        flag = flag_bytes.decode('utf-8', errors='ignore')
                        if flag.startswith('GPNCTF{') or 'flag' in flag.lower() or 'gpn' in flag.lower():
                            print(f"Potential FLAG with text '{test_text}': {flag}")
                    except:
                        pass
        
        r.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
