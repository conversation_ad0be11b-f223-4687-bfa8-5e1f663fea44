#!/usr/bin/env python3

from pwn import *
from hashlib import sha512

def decrypt_block(oracle_func, target_block, prev_block):
    """Decrypt a single block using padding oracle attack"""
    print(f"Decrypting block...")
    
    # Intermediate values (what we get after AES decryption, before XOR with prev block)
    intermediate = bytearray(16)
    
    # Work backwards through each byte position
    for pos in range(15, -1, -1):
        print(f"  Position {pos}", end="")
        
        # Create a modified previous block
        modified_prev = bytearray(16)
        
        # Set up the padding for positions we've already found
        padding_value = 16 - pos
        for i in range(pos + 1, 16):
            modified_prev[i] = intermediate[i] ^ padding_value
        
        # Try all possible byte values for current position
        found = False
        for guess in range(256):
            modified_prev[pos] = guess
            
            # Test with oracle
            test_ciphertext = bytes(modified_prev) + target_block
            
            if oracle_func(test_ciphertext):
                # Valid padding found
                intermediate[pos] = guess ^ padding_value
                print(f" -> {intermediate[pos]:02x}")
                found = True
                break
        
        if not found:
            print(f" -> FAILED")
            return None
    
    # XOR with previous block to get actual plaintext
    plaintext_block = bytes(intermediate[i] ^ prev_block[i] for i in range(16))
    print(f"Block decrypted: {plaintext_block}")
    print(f"Block hex: {plaintext_block.hex()}")
    
    return plaintext_block

def main():
    # Connect to the server
    host = "stormtown-of-constant-abundance.gpn23.ctf.kitctf.de"
    port = 443
    
    print(f"Connecting to {host}:{port}")
    
    try:
        # Use SSL connection
        r = remote(host, port, ssl=True)
        
        # Read the initial output
        r.recvuntil(b"Welcome to the Pad Server!\n")
        
        # Get the XORed flag
        xored_flag_hex = r.recvline().strip().decode()
        print(f"XORed flag: {xored_flag_hex}")
        
        # Get the encrypted challenge
        encrypted_challenge_hex = r.recvline().strip().decode()
        print(f"Encrypted challenge: {encrypted_challenge_hex}")
        
        encrypted_challenge = bytes.fromhex(encrypted_challenge_hex)
        print(f"Encrypted challenge length: {len(encrypted_challenge)} bytes")
        
        # Split into blocks
        blocks = [encrypted_challenge[i:i+16] for i in range(0, len(encrypted_challenge), 16)]
        print(f"Number of blocks: {len(blocks)}")
        
        # Define oracle function
        def oracle(ciphertext):
            try:
                r.sendlineafter(b"speak to the oracle: ", ciphertext.hex().encode())
                response = r.recvline()
                return b"True" in response
            except Exception as e:
                print(f"Oracle error: {e}")
                return False
        
        # Decrypt the last few blocks
        decrypted_blocks = []
        
        # Start from the last block and work backwards
        for i in range(len(blocks) - 1, max(0, len(blocks) - 5), -1):  # Last 4 blocks
            print(f"\nDecrypting block {i}/{len(blocks)-1}")
            
            target_block = blocks[i]
            prev_block = blocks[i-1] if i > 0 else b'\x00' * 16
            
            decrypted = decrypt_block(oracle, target_block, prev_block)
            
            if decrypted is None:
                print(f"Failed to decrypt block {i}")
                break
            
            decrypted_blocks.insert(0, decrypted)  # Insert at beginning
        
        if decrypted_blocks:
            # Combine decrypted blocks
            combined = b''.join(decrypted_blocks)
            print(f"\nDecrypted data: {combined}")
            print(f"Decrypted hex: {combined.hex()}")
            
            # Try to decode as text
            try:
                text = combined.decode('utf-8', errors='ignore')
                print(f"Decrypted text: '{text}'")
                
                # Look for padding
                if text:
                    # Remove padding
                    last_char = text[-1]
                    if ord(last_char) <= 16:
                        # Might be padding
                        pad_len = ord(last_char)
                        if text[-pad_len:] == last_char * pad_len:
                            text = text[:-pad_len]
                            print(f"After removing padding: '{text}'")
                
                # Now try to get the flag
                # The server does: xor(sha512(text[:-3].encode("utf-8")).digest(), FLAG.encode())
                # But we only have the end of the text, so let's see what we can do
                
                print(f"\nTrying to work with partial text...")
                print(f"Text length: {len(text)}")
                
                # If we have enough text, try different approaches
                if len(text) >= 3:
                    # Try using the text as-is (might be the full text)
                    text_for_hash = text[:-3] if len(text) > 3 else text
                    hash_digest = sha512(text_for_hash.encode("utf-8")).digest()
                    
                    xored_flag_bytes = bytes.fromhex(xored_flag_hex)
                    
                    # XOR to get the flag
                    flag_bytes = bytes(a ^ b for a, b in zip(hash_digest, xored_flag_bytes))
                    
                    try:
                        flag = flag_bytes.decode('utf-8', errors='ignore')
                        print(f"Potential FLAG: {flag}")
                    except:
                        print(f"Flag bytes: {flag_bytes.hex()}")
                
            except Exception as e:
                print(f"Error processing text: {e}")
        
        r.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
