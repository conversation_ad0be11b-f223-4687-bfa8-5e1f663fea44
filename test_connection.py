#!/usr/bin/env python3

from pwn import *
from hashlib import sha512

def main():
    # Connect to the server
    host = "stormtown-of-constant-abundance.gpn23.ctf.kitctf.de"
    port = 443
    
    print(f"Connecting to {host}:{port}")
    
    try:
        # Use SSL connection
        r = remote(host, port, ssl=True)
        
        # Read the initial output
        r.recvuntil(b"Welcome to the Pad Server!\n")
        
        # Get the XORed flag
        xored_flag_hex = r.recvline().strip().decode()
        print(f"XORed flag: {xored_flag_hex}")
        
        # Get the encrypted challenge
        encrypted_challenge_hex = r.recvline().strip().decode()
        print(f"Encrypted challenge: {encrypted_challenge_hex}")
        
        encrypted_challenge = bytes.fromhex(encrypted_challenge_hex)
        print(f"Encrypted challenge length: {len(encrypted_challenge)} bytes")
        print(f"Number of blocks: {len(encrypted_challenge) // 16}")
        
        # Test the oracle with the original ciphertext (should return True)
        print("Testing oracle with original ciphertext...")
        r.sendlineafter(b"speak to the oracle: ", encrypted_challenge_hex.encode())
        response = r.recvline()
        print(f"Oracle response: {response}")
        
        # Test with modified ciphertext (should return False)
        print("Testing oracle with modified ciphertext...")
        modified = bytearray(encrypted_challenge)
        modified[0] ^= 1  # Flip one bit
        r.sendlineafter(b"speak to the oracle: ", modified.hex().encode())
        response = r.recvline()
        print(f"Oracle response: {response}")
        
        # Try to understand the structure by testing different modifications
        print("Testing different modifications...")
        
        # Test with just the last two blocks
        last_two_blocks = encrypted_challenge[-32:]
        print(f"Testing last two blocks: {len(last_two_blocks)} bytes")
        r.sendlineafter(b"speak to the oracle: ", last_two_blocks.hex().encode())
        response = r.recvline()
        print(f"Oracle response for last two blocks: {response}")
        
        r.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
