#!/usr/bin/env python3

from pwn import *
import sys
from hashlib import sha512

def padding_oracle_attack_optimized(oracle_func, ciphertext, start_block=1, max_blocks=5):
    """
    Perform a padding oracle attack to decrypt the ciphertext (optimized version)
    Only decrypt the last few blocks to find the padding and get some plaintext
    """
    # Split into blocks (16 bytes each for AES)
    blocks = [ciphertext[i:i+16] for i in range(0, len(ciphertext), 16)]

    if len(blocks) < 2:
        print("Need at least 2 blocks for padding oracle attack")
        return None

    plaintext_blocks = []

    # Start from the end and work backwards (last few blocks)
    start_idx = max(1, len(blocks) - max_blocks)

    # Decrypt each block (except the first one which is IV)
    for block_idx in range(start_idx, len(blocks)):
        print(f"Attacking block {block_idx}/{len(blocks)-1}")

        target_block = blocks[block_idx]
        prev_block = blocks[block_idx - 1]

        # Decrypt this block
        decrypted_block = bytearray(16)

        # Work backwards through each byte position
        for pos in range(15, -1, -1):
            print(f"  Position {pos}")

            # Create a modified previous block
            modified_prev = bytearray(prev_block)

            # Set up the padding for positions we've already found
            padding_value = 16 - pos
            for i in range(pos + 1, 16):
                modified_prev[i] = decrypted_block[i] ^ padding_value

            # Try all possible byte values for current position
            found = False
            for guess in range(256):
                modified_prev[pos] = guess

                # Test with oracle
                test_ciphertext = bytes(modified_prev) + target_block

                if oracle_func(test_ciphertext):
                    # Valid padding found
                    decrypted_block[pos] = guess ^ padding_value
                    print(f"    Found byte {pos}: {decrypted_block[pos]:02x}")
                    found = True
                    break

            if not found:
                print(f"    Failed to find byte at position {pos}")
                return None

        # XOR with previous block to get actual plaintext
        plaintext_block = bytes(decrypted_block[i] ^ prev_block[i] for i in range(16))
        plaintext_blocks.append(plaintext_block)
        print(f"Block {block_idx} decrypted: {plaintext_block}")

    return b''.join(plaintext_blocks)

def unpad_pkcs7(data):
    """Remove PKCS#7 padding"""
    if not data:
        return data

    pad_len = data[-1]
    if pad_len > 16 or pad_len == 0:
        return data

    # Check if padding is valid
    for i in range(pad_len):
        if data[-(i+1)] != pad_len:
            return data

    return data[:-pad_len]

def decrypt_last_block_only(oracle_func, ciphertext):
    """Decrypt only the last block to understand the padding"""
    blocks = [ciphertext[i:i+16] for i in range(0, len(ciphertext), 16)]

    if len(blocks) < 2:
        return None

    # Get last block and second-to-last block
    target_block = blocks[-1]
    prev_block = blocks[-2]

    print(f"Decrypting last block only...")

    # Decrypt this block
    decrypted_block = bytearray(16)

    # Work backwards through each byte position
    for pos in range(15, -1, -1):
        print(f"  Position {pos}")

        # Create a modified previous block
        modified_prev = bytearray(prev_block)

        # Set up the padding for positions we've already found
        padding_value = 16 - pos
        for i in range(pos + 1, 16):
            modified_prev[i] = decrypted_block[i] ^ padding_value

        # Try all possible byte values for current position
        found = False
        for guess in range(256):
            modified_prev[pos] = guess

            # Test with oracle
            test_ciphertext = bytes(modified_prev) + target_block

            if oracle_func(test_ciphertext):
                # Valid padding found
                decrypted_block[pos] = guess ^ padding_value
                print(f"    Found byte {pos}: {decrypted_block[pos]:02x}")
                found = True
                break

        if not found:
            print(f"    Failed to find byte at position {pos}")
            return None

    # XOR with previous block to get actual plaintext
    plaintext_block = bytes(decrypted_block[i] ^ prev_block[i] for i in range(16))
    print(f"Last block decrypted: {plaintext_block}")
    print(f"Last block hex: {plaintext_block.hex()}")

    return plaintext_block

def main():
    # Connect to the server
    host = "stormtown-of-constant-abundance.gpn23.ctf.kitctf.de"
    port = 443
    
    print(f"Connecting to {host}:{port}")
    
    # Use SSL connection
    r = remote(host, port, ssl=True)
    
    # Read the initial output
    r.recvuntil(b"Welcome to the Pad Server!\n")
    
    # Get the XORed flag
    xored_flag_hex = r.recvline().strip().decode()
    print(f"XORed flag: {xored_flag_hex}")
    
    # Get the encrypted challenge
    encrypted_challenge_hex = r.recvline().strip().decode()
    print(f"Encrypted challenge: {encrypted_challenge_hex}")
    
    encrypted_challenge = bytes.fromhex(encrypted_challenge_hex)
    print(f"Encrypted challenge length: {len(encrypted_challenge)} bytes")
    
    # Define oracle function
    def oracle(ciphertext):
        try:
            r.sendlineafter(b"speak to the oracle: ", ciphertext.hex().encode())
            response = r.recvline()
            return b"True" in response
        except:
            return False
    
    # First try to decrypt just the last block to understand the structure
    print("Decrypting last block first...")
    last_block = decrypt_last_block_only(oracle, encrypted_challenge)

    if last_block:
        print(f"Last block contains: {last_block}")
        try:
            print(f"Last block as text: {last_block.decode('utf-8', errors='ignore')}")
        except:
            pass

    # Perform padding oracle attack (only last few blocks)
    print("Starting padding oracle attack...")
    decrypted = padding_oracle_attack_optimized(oracle, encrypted_challenge, max_blocks=10)
    
    if decrypted:
        print(f"Decrypted (raw): {decrypted}")
        print(f"Decrypted (hex): {decrypted.hex()}")
        
        # Remove padding
        unpadded = unpad_pkcs7(decrypted)
        print(f"Unpadded: {unpadded}")
        
        try:
            text = unpadded.decode('utf-8')
            print(f"Decrypted text: {text}")
            
            # Calculate the flag
            # The server does: xor(sha512(text[:-3].encode("utf-8")).digest(), FLAG.encode())
            text_for_hash = text[:-3]  # Remove last 3 characters
            hash_digest = sha512(text_for_hash.encode("utf-8")).digest()
            
            xored_flag_bytes = bytes.fromhex(xored_flag_hex)
            
            # XOR to get the flag
            flag_bytes = bytes(a ^ b for a, b in zip(hash_digest, xored_flag_bytes))
            flag = flag_bytes.decode('utf-8', errors='ignore')
            
            print(f"FLAG: {flag}")
            
        except Exception as e:
            print(f"Error decoding: {e}")
    
    r.close()

if __name__ == "__main__":
    main()
